import random

def get_emoji(type):
    emojis = {
        'happy': ['😄', '😊', '🙂', '😁'],
        'party': ['🎉', '🎊', '🥳', '🎈'],
        'ghost': ['👻', '💀', '🙀', '😱'],
        'detective': ['🕵️', '🔍', '👀', '🧐'],
        'alligator': ['🐊', '👋', '🚶', '🏃'],
        'wave': ['👋', '✌️', '🤙', '🖖'],
        'wink': ['😉', '😏', '😜', '🤫']
    }
    return random.choice(emojis.get(type, ['😎']))

def get_funny_message(context):
    messages = {
        'dashboard': [
            "Welcome to Hostel Command Central! No student escapes our watchful eyes!",
            "Tracking students like a GPS for parents since 2023!",
            "The hostel - where 'I was in my room' excuses come to die!",
            "Attendance tracking: Because 'my dog ate my room key' isn't valid here!"
        ],
        'login_failed': [
            "Access denied! Did you forget your password or is it just Monday?",
            "Wrong credentials! Even the hostel ghost knows the right password!",
            "Login failed! Have you tried turning yourself off and on again?"
        ],
        'student_added': [
            "Another student joins the hostel circus!",
            "Fresh meat for the attendance system!",
            "Student added successfully! May the odds be ever in their favor!"
        ],
        'check_in': [
            "Student captured in the hostel perimeter!",
            "One more student accounted for! Parents everywhere rejoice!",
            "Check-in successful! Their bed will be grateful for the company!"
        ],
        'check_out': [
            "Student released back into the wild!",
            "Check-out complete! One less person to feed tonight!",
            "They've escaped! Quick, alert the village!"
        ]
    }
    return random.choice(messages.get(context, ["Something funny happened!"]))