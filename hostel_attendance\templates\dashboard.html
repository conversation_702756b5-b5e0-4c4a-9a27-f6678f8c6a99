{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body">
                <h2 class="card-title">Dashboard</h2>
                <p class="text-muted">{{ funny_message }}</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <h3 class="display-4">{{ present_count }}</h3>
                <p class="lead">Students Present</p>
                <div class="progress">
                    <div class="progress-bar bg-success" style="width: {{ (present_count / total_students * 100) if total_students else 0 }}%"></div>
                </div>
                <p class="mt-2 text-muted">Out of {{ total_students }} total students</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title">Recent Activity</h5>
                <div class="list-group">
                    {% for activity in recent_activity %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ activity.student.name }}</h6>
                            <small>{{ activity.check_in.strftime('%H:%M') }}</small>
                        </div>
                        <p class="mb-1">
                            {% if activity.check_out %}
                            Checked out at {{ activity.check_out.strftime('%H:%M') }}
                            {% else %}
                            Currently checked in
                            {% endif %}
                        </p>
                        <small>Room {{ activity.student.room_number }}</small>
                    </div>
                    {% else %}
                    <div class="list-group-item">No recent activity</div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title">Quick Actions</h5>
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ url_for('main.students') }}" class="btn btn-primary">
                        <i class="fas fa-user-check"></i> Manage Attendance
                    </a>
                    <a href="{{ url_for('main.reports') }}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> View Reports
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}