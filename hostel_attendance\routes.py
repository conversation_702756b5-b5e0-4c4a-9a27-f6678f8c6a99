from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from models import db, User, Student, Attendance
from datetime import datetime, date
from utils import get_funny_message, get_emoji

# Blueprints
auth_bp = Blueprint('auth', __name__)
main_bp = Blueprint('main', __name__)

# Authentication routes
@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            login_user(user)
            flash(f"Welcome back, attendance wizard! {get_emoji('happy')}")
            return redirect(url_for('main.dashboard'))
        
        flash(f"Login failed! Did the hostel ghost steal your credentials? {get_emoji('ghost')}")
    
    return render_template('login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash(f"You've escaped the hostel duties... for now! {get_emoji('wave')}")
    return redirect(url_for('auth.login'))

# Main routes
@main_bp.route('/')
@login_required
def dashboard():
    today = date.today()
    present_count = Attendance.query.filter(
        Attendance.date == today,
        Attendance.check_out.is_(None)
    ).count()
    
    total_students = Student.query.count()
    recent_activity = Attendance.query.order_by(Attendance.check_in.desc()).limit(5).all()
    
    return render_template(
        'dashboard.html', 
        present_count=present_count,
        total_students=total_students,
        recent_activity=recent_activity,
        funny_message=get_funny_message('dashboard')
    )

@main_bp.route('/students')
@login_required
def students():
    all_students = Student.query.all()
    return render_template('students.html', students=all_students)

@main_bp.route('/student/add', methods=['POST'])
@login_required
def add_student():
    name = request.form.get('name')
    room = request.form.get('room')
    phone = request.form.get('phone')
    email = request.form.get('email')
    
    new_student = Student(name=name, room_number=room, phone=phone, email=email)
    db.session.add(new_student)
    db.session.commit()
    
    flash(f"Student {name} added! They can't hide from attendance now! {get_emoji('detective')}")
    return redirect(url_for('main.students'))

@main_bp.route('/attendance/check-in/<int:student_id>')
@login_required
def check_in(student_id):
    student = Student.query.get_or_404(student_id)
    
    # Check if student is already checked in
    existing = Attendance.query.filter_by(
        student_id=student_id,
        date=date.today(),
        check_out=None
    ).first()
    
    if existing:
        flash(f"{student.name} is already checked in! No duplicates allowed! {get_emoji('wink')}")
    else:
        attendance = Attendance(student_id=student_id)
        db.session.add(attendance)
        db.session.commit()
        flash(f"{student.name} checked in! Welcome to the hostel party! {get_emoji('party')}")
    
    return redirect(url_for('main.students'))

@main_bp.route('/attendance/check-out/<int:student_id>')
@login_required
def check_out(student_id):
    student = Student.query.get_or_404(student_id)
    
    # Find current check-in record
    record = Attendance.query.filter_by(
        student_id=student_id,
        date=date.today(),
        check_out=None
    ).first()
    
    if record:
        record.check_out = datetime.utcnow()
        db.session.commit()
        flash(f"{student.name} checked out! See ya later, alligator! {get_emoji('alligator')}")
    else:
        flash(f"Can't check out {student.name} - they weren't checked in! Sneaky! {get_emoji('detective')}")
    
    return redirect(url_for('main.students'))

@main_bp.route('/reports')
@login_required
def reports():
    report_date = request.args.get('date', date.today().isoformat())
    
    # Get all attendance for the specified date
    attendance = Attendance.query.filter_by(date=report_date).all()
    
    # Get all students who are absent (no attendance record for the date)
    present_ids = [a.student_id for a in attendance]
    absent_students = Student.query.filter(~Student.id.in_(present_ids)).all() if present_ids else Student.query.all()
    
    return render_template(
        'reports.html', 
        attendance=attendance,
        absent_students=absent_students,
        report_date=report_date
    )